import os
import asyncio
import shutil
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

from ..api.comfy_client import ComfyUIClient
from ..api.schemas import TaskInfo, TaskStatus, BatchProgress


class BatchProcessingService:
    """批量图片处理服务"""
    
    def __init__(self):
        self.comfy_client = ComfyUIClient()
        self.supported_image_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.webp', '.gif'}
    
    def get_image_files(self, input_dir: str) -> List[str]:
        """获取输入目录中的所有图片文件"""
        if not os.path.exists(input_dir):
            raise ValueError(f"输入目录不存在: {input_dir}")
        
        image_files = []
        for file_path in Path(input_dir).iterdir():
            if file_path.is_file() and file_path.suffix.lower() in self.supported_image_extensions:
                image_files.append(str(file_path))
        
        return sorted(image_files)
    
    async def process_batch_images(
        self, 
        task: TaskInfo,
        workflow_json: Dict[str, Any],
        input_dir: str,
        output_dir: str,
        image_input_node_id: str = None
    ) -> None:
        """批量处理图片"""
        try:
            # 获取所有图片文件
            image_files = self.get_image_files(input_dir)
            if not image_files:
                raise ValueError(f"输入目录中没有找到图片文件: {input_dir}")
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 初始化批量进度
            task.batch_progress = BatchProgress(
                current_index=0,
                total_count=len(image_files),
                completed_files=[],
                failed_files=[]
            )
            
            print(f"🖼️ 开始批量处理 {len(image_files)} 张图片")
            
            for index, image_file in enumerate(image_files):
                try:
                    # 更新当前处理状态
                    task.batch_progress.current_index = index + 1
                    task.batch_progress.current_filename = os.path.basename(image_file)
                    
                    print(f"📸 处理第 {index + 1}/{len(image_files)} 张图片: {task.batch_progress.current_filename}")
                    
                    # 更新工作流中的图片输入
                    updated_workflow = self._update_image_input(workflow_json, image_file, image_input_node_id)
                    
                    # 提交到ComfyUI
                    response = await self.comfy_client.submit_prompt(updated_workflow)
                    prompt_id = response.prompt_id
                    
                    print(f"✅ 图片 {task.batch_progress.current_filename} 已提交，prompt_id: {prompt_id}")
                    
                    # 等待处理完成
                    await self._wait_for_completion(prompt_id, task)

                    # 提取生成的图片
                    try:
                        saved_files = await self._extract_output_images(prompt_id, output_dir, task.batch_progress.current_filename)

                        # 标记为完成
                        task.batch_progress.completed_files.append(task.batch_progress.current_filename)
                        print(f"✅ 图片 {task.batch_progress.current_filename} 处理完成，生成了 {len(saved_files)} 个文件")

                    except Exception as extract_error:
                        print(f"⚠️ 提取图片 {task.batch_progress.current_filename} 的输出失败: {str(extract_error)}")
                        # 即使提取失败，也标记为失败而不是中断整个批量处理
                        task.batch_progress.failed_files.append(task.batch_progress.current_filename)
                        continue
                    
                except Exception as e:
                    print(f"❌ 处理图片 {os.path.basename(image_file)} 失败: {str(e)}")
                    task.batch_progress.failed_files.append(os.path.basename(image_file))
                    continue
            
            # 批量处理完成
            completed_count = len(task.batch_progress.completed_files)
            failed_count = len(task.batch_progress.failed_files)
            
            print(f"🎉 批量处理完成！成功: {completed_count}, 失败: {failed_count}")
            
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            task.result = {
                "batch_summary": {
                    "total_processed": len(image_files),
                    "successful": completed_count,
                    "failed": failed_count,
                    "output_dir": output_dir
                }
            }
            
        except Exception as e:
            print(f"❌ 批量处理失败: {str(e)}")
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.now()
    
    def _update_image_input(self, workflow: Dict[str, Any], image_path: str, node_id: str = None) -> Dict[str, Any]:
        """更新工作流中的图片输入节点"""
        import copy
        updated_workflow = copy.deepcopy(workflow)

        # 如果没有指定节点ID，尝试自动查找图片输入节点
        if not node_id:
            node_id = self._find_image_input_node(workflow)

        if node_id and node_id in updated_workflow:
            # 确保图片文件在ComfyUI的input目录中，获取相对文件名
            relative_filename = self._copy_image_to_comfyui_input(image_path)

            # 更新图片路径
            if "inputs" in updated_workflow[node_id]:
                updated_workflow[node_id]["inputs"]["image"] = relative_filename
                print(f"🔄 已更新节点 {node_id} 的图片输入: {relative_filename}")
        else:
            print(f"⚠️ 未找到图片输入节点，节点ID: {node_id}")

        return updated_workflow
    
    def _find_image_input_node(self, workflow: Dict[str, Any]) -> Optional[str]:
        """自动查找图片输入节点"""
        for node_id, node_data in workflow.items():
            if isinstance(node_data, dict):
                class_type = node_data.get("class_type", "")
                if "LoadImage" in class_type or "Image" in class_type:
                    return node_id
        return None
    
    def _copy_image_to_comfyui_input(self, image_path: str) -> str:
        """将图片复制到ComfyUI的input目录，返回相对路径"""
        # 这里需要根据实际的ComfyUI配置来设置input目录
        comfyui_input_dir = os.getenv("COMFYUI_INPUT_DIR", "ComfyUI/input")

        filename = os.path.basename(image_path)

        if os.path.exists(comfyui_input_dir):
            dest_path = os.path.join(comfyui_input_dir, filename)
            shutil.copy2(image_path, dest_path)
            print(f"📁 图片已复制到ComfyUI输入目录: {filename}")
        else:
            print(f"⚠️ ComfyUI输入目录不存在，使用原始路径: {comfyui_input_dir}")

        return filename
    
    async def _wait_for_completion(self, prompt_id: str, task: TaskInfo) -> None:
        """等待ComfyUI任务完成"""
        max_retries = 60  # 最多等待5分钟
        
        for retry in range(max_retries):
            await asyncio.sleep(5)
            
            try:
                history = await self.comfy_client.get_history(prompt_id)
                if history:
                    history_item = history[0]
                    
                    # 更新任务进度
                    if task.progress is None:
                        task.progress = {}
                    task.progress[f"prompt_{prompt_id}"] = {
                        "status": "processing",
                        "history": history_item.model_dump()
                    }
                    
                    if history_item.outputs:
                        print(f"✅ 任务 {prompt_id} 完成")
                        task.progress[f"prompt_{prompt_id}"]["status"] = "completed"
                        return
                        
            except Exception as e:
                print(f"⚠️ 检查任务状态失败: {str(e)}")
                continue
        
        # 超时
        raise Exception(f"任务 {prompt_id} 执行超时")
    
    async def _extract_output_images(self, prompt_id: str, output_dir: str, original_filename: str) -> List[str]:
        """从ComfyUI输出目录提取生成的图片，返回保存的文件路径列表"""
        saved_files = []

        try:
            # 获取任务历史以找到输出文件
            history = await self.comfy_client.get_history(prompt_id)
            if not history:
                print(f"⚠️ 未找到任务 {prompt_id} 的历史记录")
                return saved_files

            if not history[0].outputs:
                print(f"⚠️ 任务 {prompt_id} 没有输出文件")
                return saved_files

            outputs = history[0].outputs

            # 遍历所有输出节点
            for node_id, node_outputs in outputs.items():
                if "images" in node_outputs:
                    for img_index, img_info in enumerate(node_outputs["images"]):
                        filename = img_info["filename"]
                        subfolder = img_info.get("subfolder", "")

                        # 从ComfyUI获取图片数据
                        image_data = await self.comfy_client.get_image(filename, subfolder)

                        # 生成输出文件名（保持原始文件名前缀）
                        name_without_ext = os.path.splitext(original_filename)[0]
                        file_ext = os.path.splitext(filename)[1] or '.png'

                        # 如果有多个输出图片，添加索引
                        if len(node_outputs["images"]) > 1:
                            output_filename = f"{name_without_ext}_output_{img_index + 1}{file_ext}"
                        else:
                            output_filename = f"{name_without_ext}_output{file_ext}"

                        output_path = os.path.join(output_dir, output_filename)

                        # 确保文件名唯一
                        counter = 1
                        while os.path.exists(output_path):
                            base_name = os.path.splitext(output_filename)[0]
                            output_filename = f"{base_name}_{counter}{file_ext}"
                            output_path = os.path.join(output_dir, output_filename)
                            counter += 1

                        # 保存到用户指定的输出目录
                        with open(output_path, 'wb') as f:
                            f.write(image_data)

                        saved_files.append(output_path)
                        print(f"💾 图片已保存: {output_path}")

            return saved_files

        except Exception as e:
            print(f"❌ 提取输出图片失败: {str(e)}")
            # 不再抛出异常，而是返回空列表，让批量处理继续
            return saved_files


# 全局批量处理服务实例
batch_service = BatchProcessingService()
